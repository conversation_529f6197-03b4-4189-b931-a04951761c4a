// Cache utility for storing classes and students data
// This will help reduce API calls and improve performance

interface CacheItem<T> {
  data: T
  timestamp: number
  expiresIn: number
}

class DataCache {
  private cache = new Map<string, CacheItem<any>>()
  private readonly DEFAULT_EXPIRY = 5 * 60 * 1000 // 5 minutes in milliseconds

  // Set data in cache with optional expiry time
  set<T>(key: string, data: T, expiresIn: number = this.DEFAULT_EXPIRY): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      expiresIn
    }
    this.cache.set(key, item)
  }

  // Get data from cache if not expired
  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    // Check if cache item has expired
    const now = Date.now()
    if (now - item.timestamp > item.expiresIn) {
      this.cache.delete(key)
      return null
    }

    return item.data as T
  }

  // Check if key exists and is not expired
  has(key: string): boolean {
    return this.get(key) !== null
  }

  // Clear specific cache entry
  delete(key: string): void {
    this.cache.delete(key)
  }

  // Clear all cache
  clear(): void {
    this.cache.clear()
  }

  // Get cache size
  size(): number {
    return this.cache.size
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.expiresIn) {
        this.cache.delete(key)
      }
    }
  }
}

// Create singleton instance
export const dataCache = new DataCache()

// Cache keys for different data types
export const CACHE_KEYS = {
  CLASSES: 'classes',
  CLASSES_WITH_NAMES: 'classes_with_names',
  STUDENTS_BY_CLASS: (className: string) => `students_${className}`,
  STUDENT_BY_ID: (id: string) => `student_${id}`,
  ALL_STUDENTS: 'all_students',
  BIRTHDAY_STUDENTS: 'birthday_students',
  PENDING_FEES: 'pending_fees',
  PAYMENT_SUMMARY: 'payment_summary'
} as const

// Cache expiry times (in milliseconds)
export const CACHE_EXPIRY = {
  SHORT: 2 * 60 * 1000,      // 2 minutes
  MEDIUM: 5 * 60 * 1000,     // 5 minutes  
  LONG: 15 * 60 * 1000,      // 15 minutes
  VERY_LONG: 60 * 60 * 1000  // 1 hour
} as const

// Utility functions for common cache operations
export const cacheUtils = {
  // Cache classes data
  setClasses: (classes: string[]) => {
    dataCache.set(CACHE_KEYS.CLASSES, classes, CACHE_EXPIRY.LONG)
  },

  getClasses: (): string[] | null => {
    return dataCache.get<string[]>(CACHE_KEYS.CLASSES)
  },

  // Cache classes with names
  setClassesWithNames: (classes: {id: string, name: string, section: string}[]) => {
    dataCache.set(CACHE_KEYS.CLASSES_WITH_NAMES, classes, CACHE_EXPIRY.LONG)
  },

  getClassesWithNames: (): {id: string, name: string, section: string}[] | null => {
    return dataCache.get<{id: string, name: string, section: string}[]>(CACHE_KEYS.CLASSES_WITH_NAMES)
  },

  // Cache students by class
  setStudentsByClass: (className: string, students: any[]) => {
    dataCache.set(CACHE_KEYS.STUDENTS_BY_CLASS(className), students, CACHE_EXPIRY.MEDIUM)
  },

  getStudentsByClass: (className: string): any[] | null => {
    return dataCache.get<any[]>(CACHE_KEYS.STUDENTS_BY_CLASS(className))
  },

  // Cache individual student
  setStudent: (id: string, student: any) => {
    dataCache.set(CACHE_KEYS.STUDENT_BY_ID(id), student, CACHE_EXPIRY.MEDIUM)
  },

  getStudent: (id: string): any | null => {
    return dataCache.get<any>(CACHE_KEYS.STUDENT_BY_ID(id))
  },

  // Cache birthday students
  setBirthdayStudents: (students: any[]) => {
    dataCache.set(CACHE_KEYS.BIRTHDAY_STUDENTS, students, CACHE_EXPIRY.SHORT)
  },

  getBirthdayStudents: (): any[] | null => {
    return dataCache.get<any[]>(CACHE_KEYS.BIRTHDAY_STUDENTS)
  },

  // Cache pending fees
  setPendingFees: (fees: any[]) => {
    dataCache.set(CACHE_KEYS.PENDING_FEES, fees, CACHE_EXPIRY.SHORT)
  },

  getPendingFees: (): any[] | null => {
    return dataCache.get<any[]>(CACHE_KEYS.PENDING_FEES)
  },

  // Clear all student-related cache when data changes
  clearStudentCache: () => {
    dataCache.delete(CACHE_KEYS.ALL_STUDENTS)
    dataCache.delete(CACHE_KEYS.BIRTHDAY_STUDENTS)
    dataCache.delete(CACHE_KEYS.PENDING_FEES)
    // Clear all students by class cache
    for (const key of dataCache['cache'].keys()) {
      if (key.startsWith('students_') || key.startsWith('student_')) {
        dataCache.delete(key)
      }
    }
  },

  // Clear all cache
  clearAll: () => {
    dataCache.clear()
  }
}

// Auto cleanup expired cache entries every 10 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    dataCache.cleanup()
  }, 10 * 60 * 1000)
}
