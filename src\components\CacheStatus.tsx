'use client'

import { useState, useEffect } from 'react'
import { dataCache, cacheUtils } from '@/lib/cache'
import { Database, Trash2, RefreshCw } from 'lucide-react'

export default function CacheStatus() {
  const [cacheSize, setCacheSize] = useState(0)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const updateCacheSize = () => {
      setCacheSize(dataCache.size())
    }

    // Update cache size initially
    updateCacheSize()

    // Update cache size every 5 seconds
    const interval = setInterval(updateCacheSize, 5000)

    return () => clearInterval(interval)
  }, [])

  const handleClearCache = () => {
    cacheUtils.clearAll()
    setCacheSize(0)
    console.log('🗑️ Cache cleared manually')
  }

  const handleCleanupCache = () => {
    dataCache.cleanup()
    setCacheSize(dataCache.size())
    console.log('🧹 Cache cleanup completed')
  }

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 transition-colors z-50"
        title="Show cache status"
      >
        <Database className="w-5 h-5" />
      </button>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-50 min-w-[250px]">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
          <Database className="w-4 h-4" />
          Cache Status
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          ×
        </button>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Cached Items:</span>
          <span className="font-medium text-gray-900">{cacheSize}</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Status:</span>
          <span className={`font-medium ${cacheSize > 0 ? 'text-green-600' : 'text-gray-500'}`}>
            {cacheSize > 0 ? 'Active' : 'Empty'}
          </span>
        </div>
      </div>

      <div className="flex gap-2 mt-3">
        <button
          onClick={handleCleanupCache}
          className="flex-1 bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors flex items-center justify-center gap-1"
          title="Clean up expired cache entries"
        >
          <RefreshCw className="w-3 h-3" />
          Cleanup
        </button>
        <button
          onClick={handleClearCache}
          className="flex-1 bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 transition-colors flex items-center justify-center gap-1"
          title="Clear all cache"
        >
          <Trash2 className="w-3 h-3" />
          Clear
        </button>
      </div>

      <div className="mt-2 text-xs text-gray-500">
        Cache improves performance by storing frequently accessed data locally.
      </div>
    </div>
  )
}
